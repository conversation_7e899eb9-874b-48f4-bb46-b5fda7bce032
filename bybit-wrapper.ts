import { RestClientV5 } from "bybit-api";
import { bybit } from "./env.ts";

export interface RateLimitInfo {
  remainingRequests: number;
  maxRequests: number;
  resetAtTimestamp: number;
}

export interface BybitAPIResponse<T = unknown> {
  retCode: number;
  retMsg: string;
  result: T;
  rateLimitApi?: RateLimitInfo;
  time?: number;
  retExtInfo?: Record<string, unknown>;
}

export interface InstrumentInfo {
  symbol: string;
  status: string;
  quoteCoin: string;
  baseCoin: string;
  contractType: string;
  leverageFilter: {
    minLeverage: string;
    maxLeverage: string;
    leverageStep: string;
  };
  lotSizeFilter: {
    maxOrderQty: string;
    minOrderQty: string;
    qtyStep: string;
    postOnlyMaxOrderQty: string;
    maxMktOrderQty: string;
    minNotionalValue: string;
  };
  priceFilter: {
    minPrice: string;
    maxPrice: string;
    tickSize: string;
  };
  [key: string]: unknown;
}

export interface TickerInfo {
  symbol: string;
  lastPrice: string;
  volume24h: string;
  price24hPcnt: string;
  [key: string]: unknown;
}

export interface KlineData {
  0: string; // timestamp
  1: string; // open
  2: string; // high
  3: string; // low
  4: string; // close
  5: string; // volume
  6: string; // turnover
}

export interface AccountBalance {
  coin: Array<{
    coin: string;
    walletBalance: string;
    availableToWithdraw: string;
    [key: string]: unknown;
  }>;
  [key: string]: unknown;
}

export interface PositionInfo {
  symbol: string;
  side: string;
  size: string;
  entryPrice: string;
  markPrice: string;
  unrealisedPnl: string;
  leverage: string;
  [key: string]: unknown;
}

export interface RequestLog {
  timestamp: number;
  method: string;
  params: Record<string, unknown>;
  success: boolean;
  response?: {
    retCode: number;
    retMsg: string;
    resultSize: number;
    hasResult: boolean;
    orderId: string | null;
    listLength: number;
  };
  error?: {
    message: string;
    name: string;
    code: string | number | null;
    retCode: number | null;
    retMsg: string | null;
  };
  duration: number;
  rateLimitInfo?: RateLimitInfo;
}

export interface PositionInfo {
  symbol: string;
  side: string;
  size: string;
  positionValue: string;
  entryPrice: string;
  markPrice: string;
  liqPrice: string;
  bustPrice: string;
  positionMM: string;
  positionIM: string;
  tpslMode: string;
  takeProfit: string;
  stopLoss: string;
  trailingStop: string;
  unrealisedPnl: string;
  cumRealisedPnl: string;
  createdTime: string;
  updatedTime: string;
  seq: number;
  isReduceOnly: boolean;
  mmrSysUpdatedTime: string;
  leverageSysUpdatedTime: string;
  autoAddMargin: number;
  leverage: string;
  positionBalance: string;
  riskId: number;
  riskLimitValue: string;
  sessionAvgPrice: string;
  delta: string;
  gamma: string;
  vega: string;
  theta: string;
  adlRankIndicator: number;
}

export interface PositionsResponse {
  category: string;
  list: PositionInfo[];
  nextPageCursor: string;
}

export interface InstrumentsCache {
  instruments: Map<string, InstrumentInfo>;
  lastUpdated: number;
  nextPageCursor?: string;
}

export class BybitWrapper {
  private client: RestClientV5;
  private lastRateLimit: RateLimitInfo | null = null;
  private instrumentsCache: InstrumentsCache = {
    instruments: new Map(),
    lastUpdated: 0
  };

  constructor() {
    this.client = new RestClientV5({
      key: bybit.apiKey,
      secret: bybit.apiSecret,
      testnet: bybit.demo,
      demoTrading: bybit.demo,
      parseAPIRateLimits: true,
    });

    // Clean up logs directory on startup
    this.cleanupLogsDirectory();
  }

  private async cleanupLogsDirectory(): Promise<void> {
    try {
      console.log("🧹 Cleaning up logs directory...");

      // Remove the entire logs directory
      try {
        await Deno.remove('./logs', { recursive: true });
      } catch {
        // Directory might not exist, that's fine
      }

      // Recreate the logs directory
      await Deno.mkdir('./logs', { recursive: true });

      console.log("✅ Logs directory cleaned");
    } catch (error) {
      console.warn("⚠️ Failed to cleanup logs directory:", error);
    }
  }

  private async handleRateLimit(): Promise<void> {
    if (this.lastRateLimit) {
      const now = Date.now();
      
      // If we're close to the limit, wait until reset
      if (this.lastRateLimit.remainingRequests <= 2) {
        const waitTime = this.lastRateLimit.resetAtTimestamp - now;
        if (waitTime > 0 && waitTime < 60000) { // Max 1 minute wait
          console.log(`⏳ Rate limit reached, waiting ${waitTime}ms...`);
          await new Promise(resolve => setTimeout(resolve, waitTime + 100));
        }
      }
    }
  }

  private updateRateLimit(response: { rateLimitApi?: RateLimitInfo }): void {
    if (response?.rateLimitApi) {
      this.lastRateLimit = response.rateLimitApi;
    }
  }

  private async logRequest<T>(method: string, params: Record<string, unknown>, apiCall: () => Promise<BybitAPIResponse<T>>): Promise<BybitAPIResponse<T>> {
    const startTime = Date.now();
    const logEntry: RequestLog = {
      timestamp: startTime,
      method,
      params: JSON.parse(JSON.stringify(params)), // Deep clone to avoid reference issues
      success: false,
      duration: 0,
    };

    try {
      console.log(`📡 API Request: ${method}`, params);
      const response = await apiCall();

      // Check if API returned an error (retCode != 0 or retMsg != "OK")
      const isApiError = response?.retCode !== 0 || (response?.retMsg && response.retMsg !== "OK");

      if (isApiError) {
        logEntry.success = false;
        logEntry.error = {
          message: response?.retMsg || 'API Error',
          name: 'APIError',
          code: response?.retCode || null,
          retCode: response?.retCode || null,
          retMsg: response?.retMsg || null
        };
        logEntry.duration = Date.now() - startTime;
        logEntry.rateLimitInfo = this.lastRateLimit ? { ...this.lastRateLimit } : undefined;

        console.error(`❌ API Error: ${method} (${logEntry.duration}ms)`, {
          retCode: response?.retCode,
          retMsg: response?.retMsg
        });

        this.saveLogToFile(logEntry);

        // Throw an error for API-level failures
        const error = new Error(`API Error: ${response?.retMsg || 'Unknown API error'}`) as Error & {
          retCode?: number;
          retMsg?: string;
        };
        error.retCode = response?.retCode;
        error.retMsg = response?.retMsg;
        throw error;
      }

      logEntry.success = true;
      // Store only essential response data instead of full response
      const result = response?.result as Record<string, unknown> | unknown[] | null;
      logEntry.response = {
        retCode: response?.retCode,
        retMsg: response?.retMsg,
        resultSize: result && typeof result === 'object' ?
          (Array.isArray(result) ? result.length : Object.keys(result).length) : 0,
        hasResult: !!response?.result,
        orderId: (result && typeof result === 'object' && 'orderId' in result) ?
          (result as { orderId: string }).orderId : null,
        listLength: (result && typeof result === 'object' && 'list' in result && Array.isArray((result as { list: unknown[] }).list)) ?
          (result as { list: unknown[] }).list.length : 0
      };
      logEntry.duration = Date.now() - startTime;
      logEntry.rateLimitInfo = this.lastRateLimit ? { ...this.lastRateLimit } : undefined;

      console.log(`✅ API Success: ${method} (${logEntry.duration}ms)`, {
        rateLimitRemaining: this.lastRateLimit?.remainingRequests,
        retCode: response?.retCode,
        retMsg: response?.retMsg
      });

      this.saveLogToFile(logEntry);
      return response;

    } catch (error) {
      // Handle HTTP-level errors or re-thrown API errors
      if (!logEntry.error) {
        logEntry.success = false;
        const errorWithExtras = error as Error & {
          retCode?: number;
          retMsg?: string;
          code?: string | number;
        };
        logEntry.error = {
          message: errorWithExtras?.message || 'Unknown error',
          name: errorWithExtras?.name || 'Error',
          code: errorWithExtras?.code || null,
          retCode: errorWithExtras?.retCode || null,
          retMsg: errorWithExtras?.retMsg || null
        };
        logEntry.duration = Date.now() - startTime;
        logEntry.rateLimitInfo = this.lastRateLimit ? { ...this.lastRateLimit } : undefined;

        console.error(`❌ API Error: ${method} (${logEntry.duration}ms)`, error);

        this.saveLogToFile(logEntry);
      }

      throw error;
    }
  }

  private async saveLogToFile(logEntry: RequestLog): Promise<void> {
    try {
      // Ensure logs directory exists
      try {
        await Deno.mkdir('./logs', { recursive: true });
      } catch {
        // Directory might already exist
      }

      const dateStr = new Date().toISOString().split('T')[0];
      const filename = logEntry.success
        ? `./logs/bybit-success-${dateStr}.json`
        : `./logs/bybit-errors-${dateStr}.json`;

      // Read existing logs or create empty array
      let existingLogs: RequestLog[] = [];
      try {
        const existingContent = await Deno.readTextFile(filename);
        existingLogs = JSON.parse(existingContent);
      } catch {
        // File doesn't exist or is empty, start with empty array
      }

      // Add new log entry
      existingLogs.push(logEntry);

      // Keep only last 1000 entries to prevent file from growing too large
      if (existingLogs.length > 1000) {
        existingLogs = existingLogs.slice(-1000);
      }

      // Write back to file
      await Deno.writeTextFile(filename, JSON.stringify(existingLogs, null, 2));

    } catch (error) {
      console.warn(`⚠️ Failed to save request log:`, error);
    }
  }

  private async loadAllInstruments(): Promise<void> {
    console.log("📋 Loading all instruments info...");
    const allInstruments: InstrumentInfo[] = [];
    let cursor: string | undefined;
    let pageCount = 0;

    do {
      pageCount++;
      console.log(`📄 Loading instruments page ${pageCount}${cursor ? ` (cursor: ${cursor.substring(0, 20)}...)` : ''}`);

      const params: { category: 'linear'; limit: number; cursor?: string } = {
        category: 'linear' as const,
        limit: 1000
      };
      if (cursor) {
        params.cursor = cursor;
      }

      const response = await this.logRequest<unknown>('getInstrumentsInfo', params, async () => {
        await this.handleRateLimit();
        const result = await this.client.getInstrumentsInfo(params);
        this.updateRateLimit(result);
        return result as unknown as BybitAPIResponse<unknown>;
      });

      const resultData = response.result as { list?: InstrumentInfo[]; nextPageCursor?: string } | null;

      if (resultData?.list) {
        const tradingInstruments = resultData.list.filter((instrument: InstrumentInfo) =>
          instrument.status === 'Trading' && instrument.quoteCoin === 'USDT'
        );
        allInstruments.push(...tradingInstruments);
        console.log(`✅ Page ${pageCount}: ${tradingInstruments.length} trading USDT instruments`);
      }

      cursor = resultData?.nextPageCursor;
    } while (cursor);

    // Update cache
    this.instrumentsCache.instruments.clear();
    allInstruments.forEach(instrument => {
      this.instrumentsCache.instruments.set(instrument.symbol, instrument);
    });
    this.instrumentsCache.lastUpdated = Date.now();

    console.log(`🎯 Loaded ${allInstruments.length} total trading USDT instruments across ${pageCount} pages`);
  }

  async getInstruments(): Promise<InstrumentInfo[]> {
    // Check if cache needs refresh (daily refresh)
    const cacheAge = Date.now() - this.instrumentsCache.lastUpdated;
    const oneDayMs = 24 * 60 * 60 * 1000;

    if (this.instrumentsCache.instruments.size === 0 || cacheAge > oneDayMs) {
      await this.loadAllInstruments();
    }

    return Array.from(this.instrumentsCache.instruments.values());
  }

  getInstrumentInfo(symbol: string): InstrumentInfo | null {
    return this.instrumentsCache.instruments.get(symbol) || null;
  }

  async getKlines(symbol: string, interval: string, limit: number = 1000, end?: number): Promise<string[][]> {
    const params = {
      category: 'linear' as const,
      symbol,
      interval: interval as '1' | '3' | '5' | '15' | '30' | '60' | '120' | '240' | '360' | '720' | 'D' | 'W' | 'M',
      limit,
      end
    };
    const response = await this.logRequest<unknown>('getKline', params, async () => {
      await this.handleRateLimit();
      const result = await this.client.getKline(params);
      this.updateRateLimit(result);
      return result as unknown as BybitAPIResponse<unknown>;
    });

    const resultData = response.result as { list?: string[][] } | null;
    return resultData?.list || [];
  }

  async getTickers(): Promise<TickerInfo[]> {
    const params = { category: 'linear' as const };
    const response = await this.logRequest<unknown>('getTickers', params, async () => {
      await this.handleRateLimit();
      const result = await this.client.getTickers(params);
      this.updateRateLimit(result);
      return result as unknown as BybitAPIResponse<unknown>;
    });

    const resultData = response.result as { list?: TickerInfo[] } | null;
    return resultData?.list || [];
  }

  async getWalletBalance(): Promise<AccountBalance | null> {
    const params = { accountType: 'UNIFIED' as const };
    const response = await this.logRequest<unknown>('getWalletBalance', params, async () => {
      await this.handleRateLimit();
      const result = await this.client.getWalletBalance(params);
      this.updateRateLimit(result);
      return result as unknown as BybitAPIResponse<unknown>;
    });

    const resultData = response.result as { list?: AccountBalance[] } | null;
    return resultData?.list?.[0] || null;
  }

  async getPositions(): Promise<PositionInfo[]> {
    const params = { category: 'linear' as const, settleCoin: 'USDT' };
    const response = await this.logRequest<unknown>('getPositionInfo', params, async () => {
      await this.handleRateLimit();
      const result = await this.client.getPositionInfo(params);
      this.updateRateLimit(result);
      return result as unknown as BybitAPIResponse<unknown>;
    });

    const resultData = response.result as { list?: PositionInfo[] } | null;
    return resultData?.list || [];
  }

  async getCurrentLeverage(symbol: string): Promise<number | null> {
    try {
      const positions = await this.getPositions();
      const position = positions.find(p => p.symbol === symbol);
      return position ? parseFloat(position.leverage) : null;
    } catch (error) {
      console.warn(`⚠️ Failed to get current leverage for ${symbol}:`, error);
      return null;
    }
  }

  async setLeverage(symbol: string, leverage: number): Promise<void> {
    // Check current leverage first to avoid unnecessary API calls
    const currentLeverage = await this.getCurrentLeverage(symbol);

    if (currentLeverage !== null && Math.abs(currentLeverage - leverage) < 0.01) {
      console.log(`✅ Leverage already set to ${leverage}x for ${symbol}, skipping`);
      return;
    }

    console.log(`⚙️ Setting leverage from ${currentLeverage || 'unknown'}x to ${leverage}x for ${symbol}`);

    const params = {
      category: 'linear' as const,
      symbol,
      buyLeverage: leverage.toString(),
      sellLeverage: leverage.toString()
    };

    try {
      await this.logRequest<unknown>('setLeverage', params, async () => {
        await this.handleRateLimit();
        const result = await this.client.setLeverage(params);
        this.updateRateLimit(result);
        return result as unknown as BybitAPIResponse<unknown>;
      });
      console.log(`✅ Leverage successfully set to ${leverage}x for ${symbol}`);
    } catch (error) {
      // Handle the specific "leverage not modified" error gracefully
      const apiError = error as Error & { retCode?: number; retMsg?: string };
      if (apiError.retCode === 110043 && apiError.retMsg === 'leverage not modified') {
        console.log(`✅ Leverage already at ${leverage}x for ${symbol} (confirmed by API)`);
        return;
      }
      throw error;
    }
  }

  async submitOrder(params: {
    symbol: string;
    side: 'Buy' | 'Sell';
    orderType: 'Market';
    qty: string;
    reduceOnly?: boolean;
    timeInForce?: 'IOC';
  }): Promise<{ orderId?: string; [key: string]: unknown } | null> {
    const fullParams = { category: 'linear' as const, ...params };
    const response = await this.logRequest<unknown>('submitOrder', fullParams, async () => {
      await this.handleRateLimit();
      const result = await this.client.submitOrder(fullParams);
      this.updateRateLimit(result);
      return result as unknown as BybitAPIResponse<unknown>;
    });

    return response.result as { orderId?: string; [key: string]: unknown } | null;
  }

  calculateQuantity(symbol: string, value: number, price: number): string | null {
    const instrumentInfo = this.getInstrumentInfo(symbol);
    if (!instrumentInfo) {
      console.warn(`⚠️ No instrument info found for ${symbol}, using fallback precision`);
      // Fallback to old logic if instrument info not available
      if (price >= 1) {
        return (value / price).toFixed(2);
      } else if (price >= 0.01) {
        return (value / price).toFixed(1);
      } else {
        return Math.floor(value / price).toString();
      }
    }

    const { lotSizeFilter } = instrumentInfo;
    const qtyStep = parseFloat(lotSizeFilter.qtyStep);
    const minOrderQty = parseFloat(lotSizeFilter.minOrderQty);
    const maxOrderQty = parseFloat(lotSizeFilter.maxOrderQty);

    // Calculate raw quantity
    const rawQuantity = value / price;

    // Round to the nearest qtyStep
    const steppedQuantity = Math.floor(rawQuantity / qtyStep) * qtyStep;

    // Ensure it meets minimum requirements
    const finalQuantity = Math.max(steppedQuantity, minOrderQty);

    // Check if it exceeds maximum
    if (finalQuantity > maxOrderQty) {
      console.warn(`⚠️ Calculated quantity ${finalQuantity} exceeds max ${maxOrderQty} for ${symbol}`);
      return maxOrderQty.toString();
    }

    // Check minimum notional value
    const minNotionalValue = parseFloat(lotSizeFilter.minNotionalValue);
    if (finalQuantity * price < minNotionalValue) {
      console.warn(`⚠️ Order value ${(finalQuantity * price).toFixed(2)} below minimum ${minNotionalValue} for ${symbol}`);
      return null;
    }

    // Format with appropriate decimal places based on qtyStep
    const decimalPlaces = qtyStep < 1 ? Math.abs(Math.log10(qtyStep)) : 0;
    return finalQuantity.toFixed(decimalPlaces);
  }

  getRateLimitInfo(): RateLimitInfo | null {
    return this.lastRateLimit;
  }
}
