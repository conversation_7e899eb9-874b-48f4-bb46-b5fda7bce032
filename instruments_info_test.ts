import { RestClientV5 } from "bybit-api";
import { bybit } from "./env.ts";

const client = new RestClientV5({
  key: bybit.apiKey,
  secret: bybit.apiSecret,
  testnet: bybit.demo,
  demoTrading: bybit.demo,
  parseAPIRateLimits: true,
});

const instrumentsInfo =await client.getInstrumentsInfo({
  category: "linear",
});

console.log(instrumentsInfo);

/*
          maxOrderQty: "162000",
          minOrderQty: "1",
          qtyStep: "1",
          postOnlyMaxOrderQty: "162000",
          maxMktOrderQty: "32400",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.008",
        lowerFundingRate: "-0.008",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.1", priceLimitRatioY: "0.2" },
        displayName: ""
      },
      {
        symbol: "AVAAIUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "AVAAI",
        quoteCoin: "USDT",
        launchTime: "1736325126000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "5",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "25.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.00001",
          maxPrice: "199.99998",
          tickSize: "0.00001"
        },
        lotSizeFilter: {
          maxOrderQty: "445000",
          minOrderQty: "1",
          qtyStep: "1",
          postOnlyMaxOrderQty: "445000",
          maxMktOrderQty: "89000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.02",
        lowerFundingRate: "-0.02",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "AVAILUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "AVAIL",
        quoteCoin: "USDT",
        launchTime: "1721745394000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "5",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "12.50",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.00001",
          maxPrice: "199.99998",
          tickSize: "0.00001"
        },
        lotSizeFilter: {
          maxOrderQty: "400000",
          minOrderQty: "1",
          qtyStep: "1",
          postOnlyMaxOrderQty: "400000",
          maxMktOrderQty: "80000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 480,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.04",
        lowerFundingRate: "-0.04",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.2", priceLimitRatioY: "0.4" },
        displayName: ""
      },
      {
        symbol: "AVAUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "AVA",
        quoteCoin: "USDT",
        launchTime: "1734343893000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "4",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "50.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.0001",
          maxPrice: "1999.9998",
          tickSize: "0.0001"
        },
        lotSizeFilter: {
          maxOrderQty: "50000.0",
          minOrderQty: "0.1",
          qtyStep: "0.1",
          postOnlyMaxOrderQty: "50000.0",
          maxMktOrderQty: "10000.0",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "none",
        upperFundingRate: "0.01",
        lowerFundingRate: "-0.01",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "AVAXUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "AVAX",
        quoteCoin: "USDT",
        launchTime: "1631664000000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "3",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "50.00",
          leverageStep: "0.01"
        },
        priceFilter: { minPrice: "0.001", maxPrice: "19999.998", tickSize: "0.001" },
        lotSizeFilter: {
          maxOrderQty: "64690.0",
          minOrderQty: "0.1",
          qtyStep: "0.1",
          postOnlyMaxOrderQty: "64690.0",
          maxMktOrderQty: "10950.0",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 480,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.01",
        lowerFundingRate: "-0.01",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.02", priceLimitRatioY: "0.1" },
        displayName: ""
      },
      {
        symbol: "AVLUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "AVL",
        quoteCoin: "USDT",
        launchTime: "1740048791000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "4",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "25.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.0001",
          maxPrice: "1999.9998",
          tickSize: "0.0001"
        },
        lotSizeFilter: {
          maxOrderQty: "375000",
          minOrderQty: "1",
          qtyStep: "1",
          postOnlyMaxOrderQty: "375000",
          maxMktOrderQty: "75000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 120,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.02",
        lowerFundingRate: "-0.02",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.2", priceLimitRatioY: "0.4" },
        displayName: ""
      },
      {
        symbol: "AWEUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "AWE",
        quoteCoin: "USDT",
        launchTime: "1747904988000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "5",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "12.50",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.00001",
          maxPrice: "199.99998",
          tickSize: "0.00001"
        },
        lotSizeFilter: {
          maxOrderQty: "1750000",
          minOrderQty: "10",
          qtyStep: "10",
          postOnlyMaxOrderQty: "1750000",
          maxMktOrderQty: "350000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "none",
        upperFundingRate: "0.04",
        lowerFundingRate: "-0.04",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "AXLUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "AXL",
        quoteCoin: "USDT",
        launchTime: "1701941550000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "4",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "50.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.0001",
          maxPrice: "1999.9998",
          tickSize: "0.0001"
        },
        lotSizeFilter: {
          maxOrderQty: "75600",
          minOrderQty: "1",
          qtyStep: "1",
          postOnlyMaxOrderQty: "75600",
          maxMktOrderQty: "24100",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 60,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.008",
        lowerFundingRate: "-0.008",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "AXSUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "AXS",
        quoteCoin: "USDT",
        launchTime: "1628726400000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "3",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "50.00",
          leverageStep: "0.01"
        },
        priceFilter: { minPrice: "0.001", maxPrice: "19999.998", tickSize: "0.001" },
        lotSizeFilter: {
          maxOrderQty: "89050.0",
          minOrderQty: "0.1",
          qtyStep: "0.1",
          postOnlyMaxOrderQty: "89050.0",
          maxMktOrderQty: "12060.0",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 480,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.01",
        lowerFundingRate: "-0.01",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.1", priceLimitRatioY: "0.2" },
        displayName: ""
      },
      {
        symbol: "B2USDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "B2",
        quoteCoin: "USDT",
        launchTime: "1749460723000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "4",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "12.50",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.0001",
          maxPrice: "1999.9998",
          tickSize: "0.0001"
        },
        lotSizeFilter: {
          maxOrderQty: "186000",
          minOrderQty: "1",
          qtyStep: "1",
          postOnlyMaxOrderQty: "186000",
          maxMktOrderQty: "37200",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "none",
        upperFundingRate: "0.04",
        lowerFundingRate: "-0.04",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "B3USDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "B3",
        quoteCoin: "USDT",
        launchTime: "1739355381000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "6",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "50.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.000001",
          maxPrice: "19.999998",
          tickSize: "0.000001"
        },
        lotSizeFilter: {
          maxOrderQty: "22000000",
          minOrderQty: "100",
          qtyStep: "100",
          postOnlyMaxOrderQty: "22000000",
          maxMktOrderQty: "4400000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 120,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.01",
        lowerFundingRate: "-0.01",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "BABYUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BABY",
        quoteCoin: "USDT",
        launchTime: "1743672875000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "5",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "50.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.00001",
          maxPrice: "199.99998",
          tickSize: "0.00001"
        },
        lotSizeFilter: {
          maxOrderQty: "1700000",
          minOrderQty: "10",
          qtyStep: "10",
          postOnlyMaxOrderQty: "1700000",
          maxMktOrderQty: "340000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 120,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.008",
        lowerFundingRate: "-0.008",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.3", priceLimitRatioY: "0.5" },
        displayName: ""
      },
      {
        symbol: "BADGERUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BADGER",
        quoteCoin: "USDT",
        launchTime: "1692174718000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "4",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "16.67",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.0010",
          maxPrice: "19999.9980",
          tickSize: "0.0010"
        },
        lotSizeFilter: {
          maxOrderQty: "31300.0",
          minOrderQty: "0.1",
          qtyStep: "0.1",
          postOnlyMaxOrderQty: "31300.0",
          maxMktOrderQty: "6840.0",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "none",
        upperFundingRate: "0.03",
        lowerFundingRate: "-0.03",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.2", priceLimitRatioY: "0.4" },
        displayName: ""
      },
      {
        symbol: "BAKEUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BAKE",
        quoteCoin: "USDT",
        launchTime: "1648794979000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "4",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "25.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.0001",
          maxPrice: "1999.9998",
          tickSize: "0.0001"
        },
        lotSizeFilter: {
          maxOrderQty: "1055240.0",
          minOrderQty: "0.1",
          qtyStep: "0.1",
          postOnlyMaxOrderQty: "1055240.0",
          maxMktOrderQty: "172570.0",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 480,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.02",
        lowerFundingRate: "-0.02",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.1", priceLimitRatioY: "0.2" },
        displayName: ""
      },
      {
        symbol: "BALUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BAL",
        quoteCoin: "USDT",
        launchTime: "1649405651000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "3",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "12.50",
          leverageStep: "0.01"
        },
        priceFilter: { minPrice: "0.001", maxPrice: "19999.998", tickSize: "0.001" },
        lotSizeFilter: {
          maxOrderQty: "26340.00",
          minOrderQty: "0.01",
          qtyStep: "0.01",
          postOnlyMaxOrderQty: "26340.00",
          maxMktOrderQty: "4902.00",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 480,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.04",
        lowerFundingRate: "-0.04",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.2", priceLimitRatioY: "0.4" },
        displayName: ""
      },
      {
        symbol: "BANANAS31USDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BANANAS31",
        quoteCoin: "USDT",
        launchTime: "1743154150000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "6",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "25.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.000001",
          maxPrice: "19.999998",
          tickSize: "0.000001"
        },
        lotSizeFilter: {
          maxOrderQty: "15000000",
          minOrderQty: "100",
          qtyStep: "100",
          postOnlyMaxOrderQty: "15000000",
          maxMktOrderQty: "3000000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.02",
        lowerFundingRate: "-0.02",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "BANANAUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BANANA",
        quoteCoin: "USDT",
        launchTime: "1721386183000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "3",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "25.00",
          leverageStep: "0.01"
        },
        priceFilter: { minPrice: "0.001", maxPrice: "19999.998", tickSize: "0.001" },
        lotSizeFilter: {
          maxOrderQty: "1500.00",
          minOrderQty: "0.01",
          qtyStep: "0.01",
          postOnlyMaxOrderQty: "1500.00",
          maxMktOrderQty: "300.00",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.02",
        lowerFundingRate: "-0.02",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.1", priceLimitRatioY: "0.2" },
        displayName: ""
      },
      {
        symbol: "BANDUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BAND",
        quoteCoin: "USDT",
        launchTime: "1645167116000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "4",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "25.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.0001",
          maxPrice: "1999.9998",
          tickSize: "0.0001"
        },
        lotSizeFilter: {
          maxOrderQty: "134450.0",
          minOrderQty: "0.1",
          qtyStep: "0.1",
          postOnlyMaxOrderQty: "134450.0",
          maxMktOrderQty: "20410.0",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 480,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.02",
        lowerFundingRate: "-0.02",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.1", priceLimitRatioY: "0.2" },
        displayName: ""
      },
      {
        symbol: "BANKUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BANK",
        quoteCoin: "USDT",
        launchTime: "*************",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "5",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "50.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.00001",
          maxPrice: "199.99998",
          tickSize: "0.00001"
        },
        lotSizeFilter: {
          maxOrderQty: "2600000",
          minOrderQty: "10",
          qtyStep: "10",
          postOnlyMaxOrderQty: "2600000",
          maxMktOrderQty: "520000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.008",
        lowerFundingRate: "-0.008",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "BANUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BAN",
        quoteCoin: "USDT",
        launchTime: "1731919190000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "5",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "12.50",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.00001",
          maxPrice: "199.99998",
          tickSize: "0.00001"
        },
        lotSizeFilter: {
          maxOrderQty: "725000",
          minOrderQty: "1",
          qtyStep: "1",
          postOnlyMaxOrderQty: "725000",
          maxMktOrderQty: "145000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 120,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.04",
        lowerFundingRate: "-0.04",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "BATUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BAT",
        quoteCoin: "USDT",
        launchTime: "1637712000000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "4",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "25.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.0001",
          maxPrice: "1999.9998",
          tickSize: "0.0001"
        },
        lotSizeFilter: {
          maxOrderQty: "1165980.0",
          minOrderQty: "0.1",
          qtyStep: "0.1",
          postOnlyMaxOrderQty: "1165980.0",
          maxMktOrderQty: "151610.0",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 480,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.02",
        lowerFundingRate: "-0.02",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.1", priceLimitRatioY: "0.2" },
        displayName: ""
      },
      {
        symbol: "BBUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BB",
        quoteCoin: "USDT",
        launchTime: "1715686860000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "4",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "25.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.0001",
          maxPrice: "1999.9998",
          tickSize: "0.0001"
        },
        lotSizeFilter: {
          maxOrderQty: "600000",
          minOrderQty: "1",
          qtyStep: "1",
          postOnlyMaxOrderQty: "600000",
          maxMktOrderQty: "120000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.02",
        lowerFundingRate: "-0.02",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "BCHPERP",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BCH",
        quoteCoin: "USDC",
        launchTime: "1746013033000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "2",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "75.00",
          leverageStep: "0.01"
        },
        priceFilter: { minPrice: "0.01", maxPrice: "199999.98", tickSize: "0.01" },
        lotSizeFilter: {
          maxOrderQty: "325.00",
          minOrderQty: "0.01",
          qtyStep: "0.01",
          postOnlyMaxOrderQty: "325.00",
          maxMktOrderQty: "65.00",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDC",
        copyTrading: "none",
        upperFundingRate: "0.0066",
        lowerFundingRate: "-0.0066",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.05", priceLimitRatioY: "0.2" },
        displayName: "BCHUSDC"
      },
      {
        symbol: "BCHUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BCH",
        quoteCoin: "USDT",
        launchTime: "1514764800000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "2",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "50.00",
          leverageStep: "0.01"
        },
        priceFilter: { minPrice: "0.10", maxPrice: "1999999.80", tickSize: "0.10" },
        lotSizeFilter: {
          maxOrderQty: "5300.00",
          minOrderQty: "0.01",
          qtyStep: "0.01",
          postOnlyMaxOrderQty: "5300.00",
          maxMktOrderQty: "804.00",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 480,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.01",
        lowerFundingRate: "-0.01",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.05", priceLimitRatioY: "0.1" },
        displayName: ""
      },
      {
        symbol: "BDXNUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BDXN",
        quoteCoin: "USDT",
        launchTime: "1748946949000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "5",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "12.50",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.00001",
          maxPrice: "199.99998",
          tickSize: "0.00001"
        },
        lotSizeFilter: {
          maxOrderQty: "1200000",
          minOrderQty: "1",
          qtyStep: "1",
          postOnlyMaxOrderQty: "1200000",
          maxMktOrderQty: "240000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "none",
        upperFundingRate: "0.04",
        lowerFundingRate: "-0.04",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "BEAMUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BEAM",
        quoteCoin: "USDT",
        launchTime: "1699955296000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "6",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "25.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.000001",
          maxPrice: "19.999998",
          tickSize: "0.000001"
        },
        lotSizeFilter: {
          maxOrderQty: "15140000",
          minOrderQty: "100",
          qtyStep: "100",
          postOnlyMaxOrderQty: "15140000",
          maxMktOrderQty: "1570000",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 240,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.02",
        lowerFundingRate: "-0.02",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.15", priceLimitRatioY: "0.3" },
        displayName: ""
      },
      {
        symbol: "BELUSDT",
        contractType: "LinearPerpetual",
        status: "Trading",
        baseCoin: "BEL",
        quoteCoin: "USDT",
        launchTime: "1655445585000",
        deliveryTime: "0",
        deliveryFeeRate: "",
        priceScale: "4",
        leverageFilter: {
          minLeverage: "1",
          maxLeverage: "25.00",
          leverageStep: "0.01"
        },
        priceFilter: {
          minPrice: "0.0001",
          maxPrice: "1999.9998",
          tickSize: "0.0001"
        },
        lotSizeFilter: {
          maxOrderQty: "289400",
          minOrderQty: "1",
          qtyStep: "1",
          postOnlyMaxOrderQty: "289400",
          maxMktOrderQty: "38900",
          minNotionalValue: "5"
        },
        unifiedMarginTrade: true,
        fundingInterval: 480,
        settleCoin: "USDT",
        copyTrading: "both",
        upperFundingRate: "0.02",
        lowerFundingRate: "-0.02",
        isPreListing: false,
        preListingInfo: null,
        riskParameters: { priceLimitRatioX: "0.1", priceLimitRatioY: "0.2" },
        displayName: ""
      },
      ... 400 more items
    ],
    nextPageCursor: "first%3D1000000BABYDOGEUSDT%26last%3DSWEATUSDT"
  },
  retExtInfo: {},
  time: 1749831319155
}
misus-MBP:gleam misu$ 
*/